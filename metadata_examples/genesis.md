These files were posted on the [Community Public File Directory](https://forum.autonomi.community/t/community-public-file-directory/41280) on the Autonomi Forum.
This was the first listing of public immutable files known on the main Autonomi network. They are listed in the Genesis Pod, the first pod of immutable files using colonylib:

## Music

c859818c623ce4fc0899c2ab43061b19caa0b0598eec35ef309dbe50c8af8d59  BegBlag.mp3
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:MediaObject",
  "@id": "ant://c859818c623ce4fc0899c2ab43061b19caa0b0598eec35ef309dbe50c8af8d59",
  "schema:name": "BegBlag.mp3",
  "schema:encodingFormat": "audio/mpeg"
}
```

350a5b3de1482e05a0c12ce7f434504d7372961036f692565c6fbf0511b0b800  AnarchyInTheSouthside.mp3
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:MediaObject",
  "@id": "ant://350a5b3de1482e05a0c12ce7f434504d7372961036f692565c6fbf0511b0b800",
  "schema:name": "AnarchyInTheSouthside.mp3",
  "schema:encodingFormat": "audio/mpeg"
}
```
076f7b3c16ced138b794084f35d581003046e363fca82fd851556af4296a2541  Best_CD_Ever.mp3
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:MediaObject",
  "@id": "ant://076f7b3c16ced138b794084f35d581003046e363fca82fd851556af4296a2541",
  "schema:name": "Best_CD_Ever.mp3",
  "schema:encodingFormat": "audio/mpeg"
}
```

cc62ca079e8c3e649a134c0ff80da86c393ad16d6c40c002e0643afd7c51d55b  Deep_Feelings_Mix.mp3
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:MediaObject",
  "@id": "ant://cc62ca079e8c3e649a134c0ff80da86c393ad16d6c40c002e0643afd7c51d55b",
  "schema:name": "Deep_Feelings_Mix.mp3",
  "schema:encodingFormat": "audio/mpeg"
}
```

4d32050ad7972af55f3f88ae6c3e0366e7bebc816a2c9990516d31a28713323f  Patosh-RoughNight.mp3
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:MediaObject",
  "@id": "ant://4d32050ad7972af55f3f88ae6c3e0366e7bebc816a2c9990516d31a28713323f",
  "schema:name": "Patosh-RoughNight.mp3",
  "schema:encodingFormat": "audio/mpeg"
}
```

bfa02e3c6af8c2b5b515a80a10f7a3389442b9e60a2419e447e42ae3e7f93cc6 Big Country live at Glasgow Apollo In A Big Country.MP3
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:MediaObject",
  "@id": "ant://bfa02e3c6af8c2b5b515a80a10f7a3389442b9e60a2419e447e42ae3e7f93cc6",
  "schema:name": "Big Country live at Glasgow Apollo In A Big Country.MP3",
  "schema:encodingFormat": "audio/mpeg",
  "schema:description": "Live performance by Big Country at Glasgow Apollo"
}
```

1b5a77a34e172ea3c6596b17eb33bc4e64aa8d2ed378d8be9a52f4a37083eb1f - A song @NAFO_Radio wrote about being a little kid.
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:MediaObject",
  "@id": "ant://1b5a77a34e172ea3c6596b17eb33bc4e64aa8d2ed378d8be9a52f4a37083eb1f",
  "schema:encodingFormat": "audio/mpeg",
  "schema:description": "A song @NAFO_Radio wrote about being a little kid",
  "schema:creator": "@NAFO_Radio"
}
```

## Videos
5a3fab6764916342f015c67b7856ba8ff2c0e711827ce18abdb3075f358c5237  An_Introduction_To_The_SafeNetwork.mp4
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:VideoObject",
  "@id": "ant://5a3fab6764916342f015c67b7856ba8ff2c0e711827ce18abdb3075f358c5237",
  "schema:name": "An_Introduction_To_The_SafeNetwork.mp4",
  "schema:encodingFormat": "video/mp4",
  "schema:description": "An introduction to the Safe Network"
}
```

5bc2a0a0f309e55c83c98d033a1711b10338eba10c8696a4af65a39e70b1e6ae  autonomi.mp4
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:VideoObject",
  "@id": "ant://5bc2a0a0f309e55c83c98d033a1711b10338eba10c8696a4af65a39e70b1e6ae",
  "schema:name": "autonomi.mp4",
  "schema:encodingFormat": "video/mp4"
}
```

4c983cd101dfc42efe705374b6598e2c3090a4893fd8455ff1f97dc149fb22bb - Metropolis (1927) DVD - 3.0 GB
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:VideoObject",
  "@id": "ant://4c983cd101dfc42efe705374b6598e2c3090a4893fd8455ff1f97dc149fb22bb",
  "schema:name": "Metropolis (1927)",
  "schema:encodingFormat": "video/mp4",
  "schema:description": "Metropolis (1927) DVD",
  "schema:contentSize": "3.0 GB",
  "schema:dateCreated": "1927"
}
```

a8fe9929f861cf4212197ed850d01b8034e1a521665ac6bb14a791f4da56276b - mp4 video of a Dragon capsule re-entering over San Francisco on 24-May-2025
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:VideoObject",
  "@id": "ant://a8fe9929f861cf4212197ed850d01b8034e1a521665ac6bb14a791f4da56276b",
  "schema:encodingFormat": "video/mp4",
  "schema:description": "mp4 video of a Dragon capsule re-entering over San Francisco on 24-May-2025",
  "schema:contentLocation": "San Francisco",
  "schema:dateCreated": "2025-05-24"
}
```

031f18a68ab3a3c63d75fa0a484f406dd8d3dabb1d009d59d9085eb8f332cc64 - VUKOVI-LaDiDa.mp4
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:VideoObject",
  "@id": "ant://031f18a68ab3a3c63d75fa0a484f406dd8d3dabb1d009d59d9085eb8f332cc64",
  "schema:name": "VUKOVI-LaDiDa.mp4",
  "schema:encodingFormat": "video/mp4",
  "schema:creator": "VUKOVI"
}
```

## Linux ISOs
e7bb1b87c1f0e07cdb76ba5e82a425a8da712940c2d3553aa6791494e92aa54d  ubuntu-16.04.6-desktop-i386.iso
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareApplication",
  "@id": "ant://e7bb1b87c1f0e07cdb76ba5e82a425a8da712940c2d3553aa6791494e92aa54d",
  "schema:name": "ubuntu-16.04.6-desktop-i386.iso",
  "schema:operatingSystem": "Ubuntu 16.04.6",
  "schema:applicationCategory": "Operating System",
  "schema:processorRequirements": "i386"
}
```

9875177d76c9768edbabe048ad2b2846b8a9de0286bd5e1097813cc0dc75128f  archlinux-2025.04.01-x86_64.iso    size: 1.2Gb
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareApplication",
  "@id": "ant://9875177d76c9768edbabe048ad2b2846b8a9de0286bd5e1097813cc0dc75128f",
  "schema:name": "archlinux-2025.04.01-x86_64.iso",
  "schema:operatingSystem": "Arch Linux",
  "schema:applicationCategory": "Operating System",
  "schema:processorRequirements": "x86_64",
  "schema:contentSize": "1.2 GB",
  "schema:dateCreated": "2025-04-01"
}
```

ccd03711ac7db31e78ed197a02f7dd2c424af6ff2b3c77f63fa7a23720438e17   linuxmint-22.1-cinnamon-64bit.iso size: 2.8Gb
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareApplication",
  "@id": "ant://ccd03711ac7db31e78ed197a02f7dd2c424af6ff2b3c77f63fa7a23720438e17",
  "schema:name": "linuxmint-22.1-cinnamon-64bit.iso",
  "schema:operatingSystem": "Linux Mint 22.1 Cinnamon",
  "schema:applicationCategory": "Operating System",
  "schema:processorRequirements": "64-bit",
  "schema:contentSize": "2.8 GB"
}
```

142925b90fc5f6e38807713303fbdc91bfcd893a3c0f597e53764087fd70b8ed   ubuntu-24.04.2-desktop-amd64.iso  size: 5.8Gb
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareApplication",
  "@id": "ant://142925b90fc5f6e38807713303fbdc91bfcd893a3c0f597e53764087fd70b8ed",
  "schema:name": "ubuntu-24.04.2-desktop-amd64.iso",
  "schema:operatingSystem": "Ubuntu 24.04.2",
  "schema:applicationCategory": "Operating System",
  "schema:processorRequirements": "amd64",
  "schema:contentSize": "5.8 GB"
}
```

## Images
63059aabaa28fb168ee45598a4540fb560e6f11df5e53a9dfac39fa413147310 - lions-chair-front.jpg
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:ImageObject",
  "@id": "ant://63059aabaa28fb168ee45598a4540fb560e6f11df5e53a9dfac39fa413147310",
  "schema:name": "lions-chair-front.jpg",
  "schema:encodingFormat": "image/jpeg",
  "schema:description": "Front view of lions chair"
}
```

6f502ed75ea1719b5bd703784a2e3e710728e13fa41702ea590e1b8b515b22ab - IA.jpg logo for Idle Asset
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:ImageObject",
  "@id": "ant://6f502ed75ea1719b5bd703784a2e3e710728e13fa41702ea590e1b8b515b22ab",
  "schema:name": "IA.jpg",
  "schema:encodingFormat": "image/jpeg",
  "schema:description": "Logo for Idle Asset"
}
```

4467c38f2591ddf840161dfd8536bfff594be4e455bf2630e841de846d49029a - A drawing of an ant girl
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:ImageObject",
  "@id": "ant://4467c38f2591ddf840161dfd8536bfff594be4e455bf2630e841de846d49029a",
  "schema:description": "A drawing of an ant girl",
  "schema:encodingFormat": "image/jpeg"
}
```

b9681387e1695bfb3ac1d73be5235db6bd852ef2b071e853ec19911928b12095 - image of venus transitting the sun on 06-Jun-2012
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:ImageObject",
  "@id": "ant://b9681387e1695bfb3ac1d73be5235db6bd852ef2b071e853ec19911928b12095",
  "schema:description": "Image of venus transitting the sun on 06-Jun-2012",
  "schema:encodingFormat": "image/jpeg",
  "schema:dateCreated": "2012-06-06"
}
```

1215a9a77405e11e3197239a509acc557f67b0e35b62e22643f67dce94b430a2 - image of total solar eclipse on 21-Aug-2017
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:ImageObject",
  "@id": "ant://1215a9a77405e11e3197239a509acc557f67b0e35b62e22643f67dce94b430a2",
  "schema:description": "Image of total solar eclipse on 21-Aug-2017",
  "schema:encodingFormat": "image/jpeg",
  "schema:dateCreated": "2017-08-21"
}
```

## Books
dcb90722cd6c7a3c66527fd8401970cad21cfc61f17e37abd421414ca26900f6 - A collection of books by Richard Feynman
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:Book",
  "@id": "ant://dcb90722cd6c7a3c66527fd8401970cad21cfc61f17e37abd421414ca26900f6",
  "schema:description": "A collection of books by Richard Feynman",
  "schema:author": "Richard Feynman"
}
```

f7e59ac54fb81d195080787ba5e3125c767324f1b0cc2f51fac069652816be1d - A collection of books by Christopher Brookmyre
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:Book",
  "@id": "ant://f7e59ac54fb81d195080787ba5e3125c767324f1b0cc2f51fac069652816be1d",
  "schema:description": "A collection of books by Christopher Brookmyre",
  "schema:author": "Christopher Brookmyre"
}
```

## Programs and Scripts
3ffa427480548a98ef71ea7d7f937dfc730db1598e0928027a47475798a08748 chunk_checker.sh
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareSourceCode",
  "@id": "ant://3ffa427480548a98ef71ea7d7f937dfc730db1598e0928027a47475798a08748",
  "schema:name": "chunk_checker.sh",
  "schema:programmingLanguage": "Shell Script"
}
```

1beecef69f9418aa82b31095f568f60e7073d0fd5c5944ba3b65a2f17e1447e6 - Doukutsu Monogatari, a freeware game
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:VideoGame",
  "@id": "ant://1beecef69f9418aa82b31095f568f60e7073d0fd5c5944ba3b65a2f17e1447e6",
  "schema:name": "Doukutsu Monogatari",
  "schema:description": "A freeware game",
  "schema:applicationCategory": "Game"
}
```

a88343401d7c67df13cf71807a810f263f72086777d5ebaffeb2eb12b7b533ae - Pubkemon Go, lo-fi AR pub crawl html game.
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:VideoGame",
  "@id": "ant://a88343401d7c67df13cf71807a810f263f72086777d5ebaffeb2eb12b7b533ae",
  "schema:name": "Pubkemon Go",
  "schema:description": "Lo-fi AR pub crawl html game",
  "schema:applicationCategory": "Game",
  "schema:genre": "Augmented Reality"
}
```

380f6a9c4e7a03fea21393bb7caeea91f70e1cc69ca55c9c791030ab1423a08e  Latest Formicaio binary for Linux
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareApplication",
  "@id": "ant://380f6a9c4e7a03fea21393bb7caeea91f70e1cc69ca55c9c791030ab1423a08e",
  "schema:name": "Formicaio",
  "schema:description": "Latest Formicaio binary for Linux",
  "schema:operatingSystem": "Linux",
  "schema:applicationCategory": "Application"
}
```

4db8f08763aca5cda31a73f0160b9217090ec54dd8b0e3e709076c563f00815d colony-cli
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareApplication",
  "@id": "ant://4db8f08763aca5cda31a73f0160b9217090ec54dd8b0e3e709076c563f00815d",
  "schema:name": "colony-cli",
  "schema:description": "colony-cli v0.1.1 x86_64 linux binary
  "schema:operatingSystem": "Linux",
  "schema:applicationCategory": "Application"
}
```

cca4e991284bfd22005bd29884079154817c7f0c3ae09c1685ffa3764c6c1e83 colony-daemon
```json
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:SoftwareApplication",
  "@id": "ant://cca4e991284bfd22005bd29884079154817c7f0c3ae09c1685ffa3764c6c1e83",
  "schema:name": "colony-daemon",
  "schema:description": "colony-daemon v0.1.2 x86_64 linux binary
  "schema:operatingSystem": "Linux",
  "schema:applicationCategory": "Application"
}
```
